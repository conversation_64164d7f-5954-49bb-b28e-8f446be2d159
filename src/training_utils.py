#!/usr/bin/env python3
"""
训练工具模块
包含训练脚本中的共享函数，消除代码重复
"""

import argparse
import os
# import platform  # 移除平台检查，Linux专用
import torch
import torch.distributed as dist
import torch.nn as nn
import random
import numpy as np
try:
    from torch.cuda.amp import GradScaler, autocast
except ImportError:
    try:
        from torch.amp import GradScaler, autocast
    except ImportError:
        GradScaler = None
        autocast = None
from typing import Dict, Optional, Any
from pathlib import Path


def set_random_seed(seed: int, deterministic: bool = True) -> None:
    """
    设置全局随机种子，确保实验可复现性

    Args:
        seed: 随机种子值
        deterministic: 是否启用确定性训练（可能影响性能）
    """
    print(f"🎯 设置随机种子: {seed}")

    # 设置Python随机种子
    random.seed(seed)

    # 设置NumPy随机种子
    np.random.seed(seed)

    # 设置PyTorch随机种子
    torch.manual_seed(seed)

    # 设置CUDA随机种子（如果可用）
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)  # 多GPU环境
        print("✅ CUDA随机种子已设置")

    # 确定性训练设置
    if deterministic:
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
        # 设置环境变量确保确定性
        os.environ['PYTHONHASHSEED'] = str(seed)
        print("✅ 启用确定性训练模式")
    else:
        # 保持性能优化
        torch.backends.cudnn.benchmark = True
        print("✅ 保持性能优化模式")


def worker_init_fn(worker_id: int) -> None:
    """
    DataLoader worker进程的随机种子初始化函数
    确保多进程数据加载的一致性

    Args:
        worker_id: worker进程ID
    """
    # 获取主进程的随机种子
    worker_seed = torch.initial_seed() % 2**32
    np.random.seed(worker_seed)
    random.seed(worker_seed)


def parse_training_cli(description: str = "训练启动器") -> argparse.Namespace:
    """
    解析训练脚本的命令行参数
    
    Args:
        description: 脚本描述
        
    Returns:
        解析后的参数
    """
    parser = argparse.ArgumentParser(description=description)
    parser.add_argument(
        "--profile",
        choices=["a6000"],
        default="a6000",
        help="硬件配置：a6000 (Linux/48GB×2)"
    )
    parser.add_argument(
        "--config", 
        default="configs/default.yaml",
        help="配置文件路径"
    )
    parser.add_argument(
        "--compile", 
        action="store_true",
        help="启用torch.compile优化 (推荐Torch≥2.1)"
    )
    parser.add_argument(
        "--amp", 
        action="store_true", 
        default=True,
        help="启用自动混合精度训练"
    )
    parser.add_argument(
        "--accum", 
        type=int, 
        default=1,
        help="梯度累积步数"
    )
    parser.add_argument(
        "--workers", 
        type=str, 
        default="auto",
        help="数据加载器worker数量，可以是数字或'auto'"
    )
    
    return parser.parse_args()


def validate_platform_compatibility(profile: str) -> None:
    """
    验证平台兼容性 - Linux专用环境

    Args:
        profile: 硬件配置文件
    """
    # 假设运行环境始终为Linux，移除Windows检查
    pass


def get_optimal_gpu() -> int:
    """
    自动检测并选择显存使用率最低的GPU

    Returns:
        最优GPU的索引，如果无可用GPU返回-1
    """
    if not torch.cuda.is_available():
        return -1

    gpu_count = torch.cuda.device_count()
    if gpu_count == 0:
        return -1

    print(f"🔍 检测到 {gpu_count} 个GPU设备")

    # 获取每个GPU的显存使用情况
    gpu_memory_info = []
    for i in range(gpu_count):
        try:
            torch.cuda.set_device(i)
            gpu_name = torch.cuda.get_device_name(i)
            total_memory = torch.cuda.get_device_properties(i).total_memory / 1e9
            allocated_memory = torch.cuda.memory_allocated(i) / 1e9
            cached_memory = torch.cuda.memory_reserved(i) / 1e9
            free_memory = total_memory - cached_memory
            usage_rate = cached_memory / total_memory

            gpu_memory_info.append({
                'index': i,
                'name': gpu_name,
                'total': total_memory,
                'allocated': allocated_memory,
                'cached': cached_memory,
                'free': free_memory,
                'usage_rate': usage_rate
            })

            print(f"   GPU {i}: {gpu_name} ({total_memory:.1f} GB)")
            print(f"      已分配: {allocated_memory:.2f} GB, 已缓存: {cached_memory:.2f} GB")
            print(f"      可用: {free_memory:.2f} GB, 使用率: {usage_rate:.1%}")

        except Exception as e:
            print(f"   GPU {i}: 无法获取信息 - {e}")
            gpu_memory_info.append({
                'index': i,
                'usage_rate': 1.0  # 设为最高使用率，避免选择
            })

    # 选择使用率最低的GPU
    optimal_gpu = min(gpu_memory_info, key=lambda x: x['usage_rate'])
    print(f"✅ 选择GPU {optimal_gpu['index']} (使用率最低: {optimal_gpu['usage_rate']:.1%})")

    return optimal_gpu['index']


def init_device_and_distributed(profile: str) -> torch.device:
    """
    初始化设备 - 单卡训练策略

    Args:
        profile: 硬件配置文件

    Returns:
        计算设备
    """
    # 验证平台兼容性
    validate_platform_compatibility(profile)

    # 设备初始化
    if not torch.cuda.is_available():
        print("⚠️ CUDA不可用，使用CPU模式")
        return torch.device("cpu")

    # 自动选择最优GPU
    optimal_gpu_id = get_optimal_gpu()
    if optimal_gpu_id == -1:
        print("⚠️ 无可用GPU，使用CPU模式")
        return torch.device("cpu")

    # 设置为单卡训练模式
    torch.cuda.set_device(optimal_gpu_id)
    device = torch.device(f"cuda:{optimal_gpu_id}")

    print(f"🎯 采用单卡训练策略，使用GPU {optimal_gpu_id}")
    print("📝 注意：已禁用分布式训练，确保训练稳定性")

    return device


def setup_hardware_optimization(profile: str, device: torch.device) -> Dict[str, Any]:
    """
    设置硬件优化 - 自动检测GPU配置

    Args:
        profile: 硬件配置文件
        device: 计算设备

    Returns:
        优化配置字典
    """
    optimization_config: Dict[str, Any] = {
        "profile": profile,
        "platform": "Linux",
        "device_type": device.type
    }

    if device.type != "cuda":
        return optimization_config

    try:
        # 基础CUDA优化
        torch.backends.cudnn.benchmark = True
        print("✅ 启用cuDNN基准模式")

        # 获取当前GPU信息
        current_gpu = device.index if device.index is not None else 0
        gpu_properties = torch.cuda.get_device_properties(current_gpu)
        gpu_name = gpu_properties.name
        total_memory_gb = gpu_properties.total_memory / 1e9

        print(f"🔧 为GPU配置优化: {gpu_name} ({total_memory_gb:.1f} GB)")

        # 基于GPU显存大小自动配置
        if total_memory_gb >= 40:  # 高端GPU (A6000, A100等)
            # 高端GPU优化配置
            torch.backends.cuda.matmul.allow_tf32 = True
            torch.backends.cudnn.allow_tf32 = True

            # 保守的内存设置 - 单卡训练策略
            memory_fraction = 0.85
            if hasattr(torch.cuda, 'set_per_process_memory_fraction'):
                torch.cuda.set_per_process_memory_fraction(memory_fraction)

            # 启用内存池优化
            if hasattr(torch.cuda, 'set_memory_pool_config'):
                torch.cuda.set_memory_pool_config(
                    max_split_size_mb=256,  # 保守的内存分片大小
                    roundup_power2_divisions=8
                )

            batch_size = 64  # 保守的批处理大小
            num_workers = 8   # 保守的worker数量
            cleanup_freq = 100

        elif total_memory_gb >= 20:  # 中端GPU (RTX 3090, RTX 4090等)
            # 中端GPU优化配置
            memory_fraction = 0.80
            if hasattr(torch.cuda, 'set_per_process_memory_fraction'):
                torch.cuda.set_per_process_memory_fraction(memory_fraction)

            batch_size = 32
            num_workers = 6
            cleanup_freq = 80

        else:  # 低端GPU
            # 低端GPU保守配置
            memory_fraction = 0.75
            if hasattr(torch.cuda, 'set_per_process_memory_fraction'):
                torch.cuda.set_per_process_memory_fraction(memory_fraction)

            batch_size = 16
            num_workers = 4
            cleanup_freq = 50

        optimization_config.update({
            "batch_size": batch_size,
            "num_workers": num_workers,
            "memory_fraction": memory_fraction,
            "cleanup_freq": cleanup_freq,
            "enable_tf32": total_memory_gb >= 40,  # 只在高端GPU启用
            "enable_compile": False,  # 禁用编译以确保稳定性
            "multi_gpu": False,  # 强制单GPU模式
            "enable_channels_last": False,  # 禁用以确保兼容性
            "enable_jit": False,  # 禁用JIT编译
            "prefetch_factor": 2,  # 保守的预取因子
            "persistent_workers": False  # 禁用持久化worker
        })

        print(f"✅ 单卡训练优化配置已应用:")
        print(f"   - GPU: {gpu_name}")
        print(f"   - 批处理大小: {batch_size}")
        print(f"   - 显存使用比例: {memory_fraction*100:.0f}%")
        print(f"   - 数据加载器worker: {num_workers}")
        print(f"   - TF32加速: {'启用' if optimization_config['enable_tf32'] else '禁用'}")

        # 清理初始内存
        torch.cuda.empty_cache()

    except Exception as e:
        print(f"⚠️ 硬件优化设置失败: {e}")

    return optimization_config


def create_optimizer(model: nn.Module, config: Dict, optimization_config: Dict) -> torch.optim.Optimizer:
    """
    创建优化器
    
    Args:
        model: 模型
        config: 训练配置
        optimization_config: 硬件优化配置
        
    Returns:
        优化器
    """
    lr = config["training"]["learning_rate"]
    weight_decay = config["training"]["weight_decay"]

    # 尝试使用FusedAdam优化器
    try:
        # 首先尝试flash-attention的FusedAdam
        from flash_attn.optim.fused_adam import FusedAdam
        optimizer = FusedAdam(model.parameters(), lr=lr, weight_decay=weight_decay)
        print("✅ 使用FusedAdam优化器 (flash-attn)")
        return optimizer
    except ImportError:
        try:
            # 回退到apex的FusedAdam
            from apex.optimizers import FusedAdam
            optimizer = FusedAdam(model.parameters(), lr=lr, weight_decay=weight_decay)
            print("✅ 使用FusedAdam优化器 (apex)")
            return optimizer
        except ImportError:
            pass

    # 回退到标准Adam
    optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=weight_decay)
    print("✅ 使用标准Adam优化器")
    return optimizer


def setup_model_compilation(model: nn.Module, args: argparse.Namespace,
                          optimization_config: Dict) -> Any:
    """
    设置模型编译优化
    
    Args:
        model: 模型
        args: 命令行参数
        optimization_config: 硬件优化配置
        
    Returns:
        编译后的模型或原模型
    """
    if args.compile and optimization_config.get("enable_compile", False):
        try:
            # 检查torch.compile可用性
            if hasattr(torch, "compile"):
                compiled_model = torch.compile(model, backend="inductor", mode="reduce-overhead")
                print("✅ 启用torch.compile优化")
                return compiled_model
            else:
                print("⚠️ torch.compile不可用，跳过编译优化")
        except Exception as e:
            print(f"⚠️ torch.compile失败: {e}")

    return model


def setup_distributed_model(model: nn.Module, optimization_config: Dict) -> nn.Module:
    """
    设置分布式模型包装 - 单卡训练模式

    Args:
        model: 模型
        optimization_config: 硬件优化配置

    Returns:
        原模型（不进行分布式包装）
    """
    # 强制单卡训练模式，不进行分布式包装
    print("📝 单卡训练模式：跳过分布式模型包装")
    return model


def cleanup_gpu_memory(optimization_config: Dict) -> None:
    """
    清理GPU内存
    
    Args:
        optimization_config: 硬件优化配置
    """
    if optimization_config["device_type"] == "cuda":
        torch.cuda.empty_cache()
        if torch.cuda.device_count() > 1:
            torch.cuda.synchronize()


def create_mixed_precision_scaler(args: argparse.Namespace, device: torch.device) -> Optional[GradScaler]:
    """
    创建混合精度训练的梯度缩放器
    
    Args:
        args: 命令行参数
        device: 计算设备
        
    Returns:
        梯度缩放器或None
    """
    if args.amp and device.type == "cuda":
        try:
            # 尝试使用新的API (PyTorch 2.x)
            from torch.amp import GradScaler as NewGradScaler
            scaler = NewGradScaler()
            print("✅ 启用混合精度训练 (新API)")
            return scaler
        except (ImportError, TypeError):
            # 回退到旧的API
            scaler = GradScaler()
            print("✅ 启用混合精度训练 (旧API)")
            return scaler
    return None
