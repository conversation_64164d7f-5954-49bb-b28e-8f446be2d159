#!/usr/bin/env python3
"""
静态模型训练脚本 - 自动GPU检测和单卡训练
支持Linux服务器环境下的稳定训练

输出路径：
- 模型参数：/data2/syd_data/Breakfast_Data/Outputs/checkpoints/static_model.pth
- 训练数据：/data2/syd_data/Breakfast_Data/Outputs/reports/static_training_history.json
"""

import argparse
import os
import sys
import yaml
# import platform  # 移除平台检查，Linux专用
from pathlib import Path
from typing import Dict, Optional, Any

# PyTorch相关导入 - 修复版本兼容性问题
try:
    import torch
    # 启用 torch.compile 时捕获 scalar outputs，避免 .item() 中断图
    torch._dynamo.config.capture_scalar_outputs = True
    import torch.distributed as dist
    import torch.nn as nn

    # 修复混合精度训练导入问题 - 优先使用新API
    try:
        # 尝试新的API (PyTorch 2.0+) - 这是当前环境支持的
        from torch.amp import GradScaler, autocast
        AMP_NEW_API = True
        print(f"✅ PyTorch {torch.__version__} - 使用新的混合精度API")
    except ImportError:
        try:
            # 回退到旧的API (PyTorch 1.x)
            from torch.cuda.amp import GradScaler, autocast
            AMP_NEW_API = False
            print(f"✅ PyTorch {torch.__version__} - 使用旧的混合精度API")
        except ImportError:
            # 如果都不可用，禁用混合精度
            GradScaler = None
            autocast = None
            AMP_NEW_API = False
            print("⚠️ 混合精度训练不可用，将使用标准精度")

    TORCH_AVAILABLE = True
except ImportError as e:
    print(f"❌ PyTorch导入失败: {e}")
    TORCH_AVAILABLE = False
    GradScaler = None
    autocast = None
    AMP_NEW_API = False

# 项目根目录设置
ROOT_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(ROOT_DIR))

# 内存分配优化：开启可扩展分段以减少碎片
os.environ.setdefault("PYTORCH_CUDA_ALLOC_CONF", "expandable_segments:True")
# 确保工作目录正确
if os.getcwd() != str(ROOT_DIR):
    os.chdir(ROOT_DIR)

# 输出目录配置 - Linux服务器环境
def get_output_dir():
    """Linux服务器环境输出目录"""
    return Path("/data2/syd_data/Breakfast_Data/Outputs")

OUTPUT_DIR = get_output_dir()
CHECKPOINT_DIR = OUTPUT_DIR / "checkpoints"
VIS_DIR = OUTPUT_DIR / "visualizations"
REPORT_DIR = OUTPUT_DIR / "reports"


def parse_cli() -> argparse.Namespace:
    """解析命令行参数 - 自动GPU检测"""
    parser = argparse.ArgumentParser(
        description="静态模型训练启动器 - 自动GPU检测和单卡训练"
    )
    parser.add_argument(
        "--config",
        default="configs/default.yaml",
        help="配置文件路径"
    )
    parser.add_argument(
        "--amp",
        action="store_true",
        default=True,
        help="启用自动混合精度训练"
    )
    parser.add_argument(
        "--compile",
        action="store_true",
        help="启用torch.compile优化（可能不稳定）"
    )
    parser.add_argument(
        "--accum",
        type=int,
        default=2,
        help="梯度累积步数"
    )
    parser.add_argument(
        "--workers",
        type=str,
        default="auto",
        help="数据加载器worker数量，可以是数字或'auto'"
    )

    return parser.parse_args()


# 使用通用训练工具中的函数
from src.training_utils import (
    validate_platform_compatibility,
    init_device_and_distributed,
    setup_hardware_optimization,
    create_optimizer,
    setup_model_compilation,
    setup_distributed_model,
    cleanup_gpu_memory,
    set_random_seed,
    worker_init_fn
)


# 硬件优化函数已移至 training_utils.py


def load_and_update_config(config_path: str, optimization_config: Dict[str, Any]) -> Dict:
    """加载并更新配置文件 - 基于自动检测的硬件配置"""
    with open(config_path, "r", encoding="utf-8") as f:
        config = yaml.safe_load(f)

    # 应用自动检测的硬件优化配置
    if optimization_config["device_type"] == "cuda":
        # 使用自动检测的批处理大小
        config["training"]["batch_size"] = optimization_config["batch_size"]
        config["training"]["num_workers"] = optimization_config["num_workers"]

        # 更新GPU优化配置
        gpu_opt = config["training"].setdefault("gpu_optimization", {})
        gpu_opt.update({
            "enable_amp": True,
            "num_workers": optimization_config["num_workers"],
            "pin_memory": True,
            "max_memory_fraction": optimization_config["memory_fraction"],
            "memory_cleanup_freq": optimization_config["cleanup_freq"],
            "enable_tf32": optimization_config.get("enable_tf32", False),
            "enable_compile": optimization_config.get("enable_compile", False),
            "persistent_workers": optimization_config.get("persistent_workers", False),
            "prefetch_factor": optimization_config.get("prefetch_factor", 2),
        })

        print(f"✅ 配置已更新为自动检测的硬件优化版本")
        print(f"   - 批处理大小: {config['training']['batch_size']}")
        print(f"   - 数据加载器worker: {config['training']['num_workers']}")
        print(f"   - 显存使用比例: {optimization_config['memory_fraction']*100:.0f}%")
        print(f"   - TF32加速: {'启用' if optimization_config.get('enable_tf32', False) else '禁用'}")

    return config


# 优化器、模型编译、分布式等函数已移至 training_utils.py


def train_epoch(model: nn.Module, train_loader, optimizer: torch.optim.Optimizer,
                scaler: Optional[GradScaler], device: torch.device,
                optimization_config: Dict, args: argparse.Namespace) -> Dict[str, float]:
    """训练一个epoch"""
    model.train()
    epoch_metrics = {"loss": 0.0, "correct": 0, "total": 0}

    cleanup_freq = optimization_config.get("cleanup_freq", 50)  # 更频繁清理
    accum_steps = max(2, args.accum)  # 至少使用2步梯度累积

    for batch_idx, batch in enumerate(train_loader):
        # 内存优化：每个batch开始时清理缓存
        if batch_idx % 10 == 0:
            torch.cuda.empty_cache()
            
        # 移动并优化批次内存格式
        _move_batch_to_device(batch, device, optimization_config)

        # 梯度清零 - 修复：在累积周期开始时清零梯度
        if batch_idx % accum_steps == 0:
            optimizer.zero_grad(set_to_none=True)

        # 前向传播和反向传播
        try:
            total_loss, outputs = _forward_backward_pass(
                model, batch, args, scaler, accum_steps
            )
        except torch.cuda.OutOfMemoryError as e:
            print(f"⚠️ 内存溢出在batch {batch_idx}，清理内存并跳过此batch")
            torch.cuda.empty_cache()
            cleanup_gpu_memory(optimization_config)
            continue

        # 优化器步骤 - 在累积周期结束时执行
        if (batch_idx + 1) % accum_steps == 0:
            _optimizer_step(optimizer, scaler, args)

        # 更新统计信息
        _update_epoch_metrics(epoch_metrics, total_loss, outputs, batch, accum_steps)

        # 定期清理和显示进度
        _periodic_maintenance(batch_idx, cleanup_freq, optimization_config, epoch_metrics)

    return {
        "loss": epoch_metrics["loss"] / len(train_loader),
        "accuracy": epoch_metrics["correct"] / epoch_metrics["total"] if epoch_metrics["total"] > 0 else 0
    }

def _move_batch_to_device(batch: Dict, device: torch.device, optimization_config: Dict) -> None:
    """移动批次数据到设备，并根据配置调整内存格式"""
    for key in ["features", "labels"]:
        if key in batch:
            tensor = batch[key].to(device, non_blocking=True)
            if key == "features" and optimization_config.get("enable_channels_last", False) and tensor.ndim == 4:
                tensor = tensor.to(memory_format=torch.channels_last)
            batch[key] = tensor

def _forward_backward_pass(model, batch, args, scaler, accum_steps):
    """执行前向和反向传播"""
    if args.amp and scaler is not None:
        return _amp_forward_backward(model, batch, scaler, accum_steps)
    else:
        return _standard_forward_backward(model, batch, accum_steps)

def _amp_forward_backward(model, batch, scaler, accum_steps):
    """混合精度前向反向传播 - 修复版本兼容性"""
    if autocast is None:
        # 如果autocast不可用，使用标准前向传播
        return _standard_forward_backward(model, batch, accum_steps)

    try:
        # 使用可用的autocast API
        if AMP_NEW_API:
            # 新API: torch.amp.autocast
            with autocast('cuda'):
                outputs = model(batch, mode="train")
                losses = model.compute_loss(outputs, batch)
                total_loss = losses["total_loss"] / accum_steps
        else:
            # 旧API: torch.cuda.amp.autocast
            with autocast():
                outputs = model(batch, mode="train")
                losses = model.compute_loss(outputs, batch)
                total_loss = losses["total_loss"] / accum_steps
    except Exception as e:
        print(f"⚠️ 混合精度前向传播失败，回退到标准精度: {e}")
        return _standard_forward_backward(model, batch, accum_steps)

    scaler.scale(total_loss).backward()
    return total_loss, outputs

def _standard_forward_backward(model, batch, accum_steps):
    """标准前向反向传播"""
    outputs = model(batch, mode="train")
    losses = model.compute_loss(outputs, batch)
    total_loss = losses["total_loss"] / accum_steps
    total_loss.backward()
    return total_loss, outputs

def _optimizer_step(optimizer, scaler, args):
    """执行优化器步骤"""
    if args.amp and scaler is not None:
        scaler.step(optimizer)
        scaler.update()
    else:
        optimizer.step()

def _update_epoch_metrics(epoch_metrics, total_loss, outputs, batch, accum_steps):
    """更新epoch统计信息"""
    epoch_metrics["loss"] += total_loss.item() * accum_steps
    predictions = torch.argmax(outputs["action_logits"], dim=-1)
    labels = batch["labels"]
    # 只统计有效标签帧 (标签>=0)
    valid_mask = labels >= 0
    epoch_metrics["correct"] += (predictions[valid_mask] == labels[valid_mask]).sum().item()
    epoch_metrics["total"] += valid_mask.sum().item()

def _periodic_maintenance(batch_idx, cleanup_freq, optimization_config, epoch_metrics):
    """定期维护：内存清理和进度显示"""
    if batch_idx % cleanup_freq == 0:
        cleanup_gpu_memory(optimization_config)

    if batch_idx % 20 == 0:
        current_acc = epoch_metrics["correct"] / epoch_metrics["total"] if epoch_metrics["total"] > 0 else 0
        print(f"   Batch {batch_idx}: Acc={current_acc:.4f}")


def save_training_data(train_history: Dict, config: Dict, optimization_config: Dict,
                      best_accuracy: float, model_path: Path) -> None:
    """
    保存训练数据和基本记录 - 移除可视化功能

    Args:
        train_history: 训练历史记录
        config: 训练配置
        optimization_config: 硬件优化配置
        best_accuracy: 最佳准确率
        model_path: 模型保存路径
    """
    try:
        import json
        from datetime import datetime

        # 保存JSON格式的训练历史
        history_path = REPORT_DIR / f"static_training_history.json"
        with open(history_path, 'w', encoding='utf-8') as f:
            json.dump({
                'train_history': train_history,
                'config': config,
                'optimization_config': optimization_config,
                'best_accuracy': best_accuracy,
                'model_path': str(model_path),
                'timestamp': datetime.now().isoformat()
            }, f, indent=2, ensure_ascii=False)

        print(f"✅ 训练数据已保存:")
        print(f"   📋 历史数据: {history_path}")
        print(f"   💾 模型文件: {model_path}")
        print(f"   🎯 最佳准确率: {best_accuracy:.4f}")

    except Exception as e:
        print(f"⚠️ 训练数据保存失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 静态模型训练启动器 - 自动GPU检测")
    print("=" * 60)

    # 解析命令行参数
    args = parse_cli()
    print(f"📋 平台: Linux")
    print(f"📋 训练策略: 单卡训练")

    try:
        # 创建输出目录
        for directory in [CHECKPOINT_DIR, VIS_DIR, REPORT_DIR]:
            directory.mkdir(parents=True, exist_ok=True)

        # 初始化设备（自动选择最优GPU）
        device = init_device_and_distributed("auto")

        # 设置硬件优化（基于检测到的GPU）
        optimization_config = setup_hardware_optimization("auto", device)

        # 加载配置
        config = load_and_update_config(args.config, optimization_config)

        # 设置随机种子确保实验可复现性
        seed = config.get("experiment", {}).get("seed", 42)
        set_random_seed(seed, deterministic=True)

        # 导入训练模块
        print("\n📦 导入训练模块...")
        from src.data_loader_fixed import create_data_loaders
        from src.model import ProceduralVideoUnderstanding
        from src.evaluation_simple import Evaluator
        print("✅ 所有训练模块导入成功")

        # 创建数据加载器
        print("🏗️ 创建数据加载器...")
        train_loader, test_loader = create_data_loaders(config, worker_init_fn=worker_init_fn)

        # 根据数据集更新动作类别数，防止配置不一致导致索引错误
        num_actions = len(train_loader.dataset.action_to_id)
        config["data"]["num_actions"] = num_actions
        print(f"✅ 动作类别数更新为: {num_actions}")

        # 创建模型
        print("🏗️ 创建模型...")
        model = ProceduralVideoUnderstanding(config).to(device)
        # channels_last 内存格式，以减少内存占用
        if optimization_config.get("enable_channels_last", False):
            model = model.to(memory_format=torch.channels_last)
            print("✅ 模型已转换为 channels_last 内存格式")

        # 模型编译优化
        model = setup_model_compilation(model, args, optimization_config)
        if args.compile:
            try:
                import torch._dynamo
                torch._dynamo.config.suppress_errors = True
                print("✅ 已启用 torch._dynamo 错误抑制 (编译/运行时错误将回退至Eager模式)")
            except ImportError:
                pass

        # 分布式模型包装
        model = setup_distributed_model(model, optimization_config)

        # 创建优化器
        optimizer = create_optimizer(model, config, optimization_config)

        # 创建学习率调度器
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer, T_max=config["training"]["num_epochs"]
        )

        # 混合精度训练 - 修复版本兼容性
        scaler = None
        if args.amp and device.type == "cuda" and GradScaler is not None:
            try:
                # 统一使用标准的GradScaler初始化
                scaler = GradScaler()
                if AMP_NEW_API:
                    print("✅ 启用混合精度训练 (新API兼容)")
                else:
                    print("✅ 启用混合精度训练 (旧API)")
            except Exception as e:
                print(f"⚠️ 混合精度训练初始化失败: {e}")
                scaler = None
        elif args.amp:
            print("⚠️ 混合精度训练不可用，使用标准精度")

        # 创建评估器
        evaluator = Evaluator(config)

        print(f"\n🎯 开始训练 ({args.profile}配置)")
        print(f"   批处理大小: {config['training']['batch_size']}")
        print(f"   训练轮数: {config['training']['num_epochs']}")

        # 训练循环
        best_accuracy = 0.0
        train_history = {"epoch": [], "train_loss": [], "train_accuracy": [], "test_accuracy": []}

        for epoch in range(config["training"]["num_epochs"]):
            print(f"\n📈 Epoch {epoch+1}/{config['training']['num_epochs']}")

            # 为分布式训练设置epoch（确保数据混洗的随机性）
            if hasattr(train_loader, 'sampler') and hasattr(train_loader.sampler, 'set_epoch'):
                train_loader.sampler.set_epoch(epoch)
                print(f"🔄 已为分布式采样器设置epoch: {epoch}")

            # 训练
            train_metrics = train_epoch(model, train_loader, optimizer, scaler,
                                      device, optimization_config, args)

            # 评估
            model.eval()
            with torch.no_grad():
                test_results = evaluator.evaluate(model, test_loader, device)

            # 更新学习率
            scheduler.step()

            # 记录历史
            train_history["epoch"].append(epoch + 1)
            train_history["train_loss"].append(train_metrics["loss"])
            train_history["train_accuracy"].append(train_metrics["accuracy"])
            train_history["test_accuracy"].append(test_results["frame_accuracy"])

            print(f"   训练损失: {train_metrics['loss']:.4f}")
            print(f"   训练准确率: {train_metrics['accuracy']:.4f}")
            print(f"   测试准确率: {test_results['frame_accuracy']:.4f}")

            # 保存最佳模型
            if test_results["frame_accuracy"] > best_accuracy:
                best_accuracy = test_results["frame_accuracy"]
                checkpoint_path = CHECKPOINT_DIR / "static_model.pth"
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'accuracy': best_accuracy,
                    'config': config,
                    'optimization_config': optimization_config
                }, checkpoint_path)
                print(f"   💾 保存最佳模型 (准确率: {best_accuracy:.4f})")

        print(f"\n🎉 训练完成！")
        print(f"   最佳准确率: {best_accuracy:.4f}")
        print(f"   模型保存位置: {CHECKPOINT_DIR}")

        # 保存训练数据
        print("\n📊 保存训练数据...")
        try:
            save_training_data(
                train_history=train_history,
                config=config,
                optimization_config=optimization_config,
                best_accuracy=best_accuracy,
                model_path=CHECKPOINT_DIR / "static_model.pth"
            )
        except Exception as save_error:
            print(f"⚠️ 训练数据保存失败: {save_error}")
            print("训练已完成，但数据保存出现问题")

    except Exception as e:
        print(f"\n❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)



